import { openai } from '@ai-sdk/openai';

export const openrouter = openai({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
});

export const geminiFlash = openrouter('google/gemini-2.0-flash-exp:free');
export const geminiPro = openrouter('google/gemini-exp-1206');
export const claude35Sonnet = openrouter('anthropic/claude-3.5-sonnet');

export const getModel = (task: 'simple' | 'complex' = 'simple') => {
  return task === 'simple' ? geminiFlash : geminiPro;
};